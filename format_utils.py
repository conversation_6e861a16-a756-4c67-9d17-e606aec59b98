#!/usr/bin/env python3
"""
记忆工具输出格式化工具
简洁美观的输出格式化，不依赖外部库
"""

import json

def format_add_memory_result(result):
    """格式化添加记忆结果 - 简洁版本"""
    if not result or result.get('status') != 'success':
        return f"❌ 添加失败: {result.get('message', '未知错误')}"

    # 检查记忆是否成功添加
    memory_result = result.get('memory_result', result.get('result', {}))
    memory_results = memory_result.get('results', [])

    if not memory_results:
        return "⚠️ 文本过于简单，LLM无法提取有效记忆内容。请尝试更具体的描述。"

    # 统计记忆操作
    add_count = sum(1 for item in memory_results if item.get('event') == 'ADD')
    update_count = sum(1 for item in memory_results if item.get('event') == 'UPDATE')

    # 实体提取结果
    entities_count = result.get('entities_count', 0)
    relations_count = result.get('relations_count', 0)

    # 构建简洁输出
    output_lines = []

    if add_count > 0:
        output_lines.append(f"✅ 新增记忆: {add_count}条")
    if update_count > 0:
        output_lines.append(f"🔄 更新记忆: {update_count}条")

    if entities_count > 0 or relations_count > 0:
        output_lines.append(f"🔍 实体提取: {entities_count}个实体, {relations_count}个关系")
    else:
        output_lines.append("⚠️ 实体提取: 未提取到实体关系")

    return "\n".join(output_lines)

def format_search_result(result):
    """格式化搜索结果 - 只显示关键信息"""
    if not result or result.get('status') != 'success':
        return f"❌ 搜索失败: {result.get('message', '未知错误')}"
    
    results = result.get('results', [])
    if not results:
        return "🔍 未找到相关记忆"
    
    output_lines = [f"🔍 找到 {len(results)} 条相关记忆:"]
    
    for i, item in enumerate(results[:3], 1):  # 只显示前3条
        memory_id = item.get('id', 'N/A')  # 显示完整ID
        memory_text = item.get('memory', 'N/A')
        score = item.get('score', 0)
        
        output_lines.append(f"  {i}. {memory_text}")
        output_lines.append(f"     ID: {memory_id}")
    
    if len(results) > 3:
        output_lines.append(f"     ... 还有 {len(results) - 3} 条结果")
    
    return "\n".join(output_lines)

def format_list_memories_result(result):
    """格式化列出记忆结果 - 只显示统计信息，避免在记忆数量庞大时输出过多内容"""
    if not result or result.get('status') != 'success':
        return f"❌ 获取失败: {result.get('message', '未知错误')}"

    memories = result.get('memories', [])
    if not memories:
        return "📝 暂无记忆"

    # 只显示记忆总数统计，不列出具体内容
    total_count = len(memories)

    # 分析记忆的时间分布（如果有时间信息）
    recent_count = 0
    for memory in memories:
        created_at = memory.get('created_at', '')
        if created_at:
            try:
                from datetime import datetime, timedelta
                dt = datetime.fromisoformat(created_at.replace('Z', '+00:00'))
                if datetime.now(dt.tzinfo) - dt < timedelta(days=7):
                    recent_count += 1
            except:
                pass

    output_lines = [
        f"📝 记忆库统计信息:",
        f"   总记忆数量: {total_count} 条",
        f"   最近7天新增: {recent_count} 条",
        f"",
        f"💡 提示: 使用 hybrid_search 工具搜索特定内容"
    ]

    return "\n".join(output_lines)

def format_delete_result(result, delete_type="single"):
    """格式化删除结果 - 简洁版本"""
    if not result or result.get('status') != 'success':
        return f"❌ 删除失败: {result.get('message', '未知错误')}"

    if delete_type == "all":
        return "✅ 所有记忆删除成功"
    else:
        return "✅ 删除成功"



def format_update_result(result):
    """格式化更新记忆结果 - 显示更新内容和实体提取"""
    if not result or result.get('status') != 'success':
        return f"❌ 更新失败: {result.get('message', '未知错误')}"

    # 获取更新的内容
    updated_content = result.get('updated_content', 'N/A')

    # 实体提取结果
    entities_count = result.get('entities_count', 0)
    relations_count = result.get('relations_count', 0)

    output_lines = [
        f"✅ 更新成功",
        f"   内容: {updated_content}"
    ]

    if entities_count > 0 or relations_count > 0:
        output_lines.append(f"🔍 实体提取: {entities_count}个实体, {relations_count}个关系")
    else:
        output_lines.append("⚠️ 实体提取: 未提取到实体关系")

    return "\n".join(output_lines)

def format_relations_result(result):
    """格式化实体关系结果"""
    if not result or result.get('status') != 'success':
        return f"❌ 获取关系失败: {result.get('message', '未知错误')}"

    relations = result.get('relations', {})
    if not relations:
        return "🔗 暂无实体关系数据"

    entity_count = relations.get('entity_count', 0)
    relationship_count = relations.get('relationship_count', 0)

    output_lines = [
        f"🔗 实体关系信息:",
        f"   实体数量: {entity_count}",
        f"   关系数量: {relationship_count}"
    ]

    entities = relations.get('entities', [])
    if entities:
        output_lines.append("   实体列表:")
        for entity in entities[:5]:  # 只显示前5个
            output_lines.append(f"     - {entity}")
        if len(entities) > 5:
            output_lines.append(f"     ... 还有 {len(entities) - 5} 个实体")

    return "\n".join(output_lines)

def format_entity_relations_result(result):
    """格式化实体关系结果"""
    if not result or result.get('status') != 'success':
        return f"❌ 获取关系失败: {result.get('message', '未知错误')}"

    relations = result.get('relations', {})
    if not relations:
        return "🔗 暂无实体关系数据"

    entity_name = relations.get('entity', 'N/A')
    related_memories = relations.get('related_memories', [])
    memory_count = relations.get('memory_count', 0)
    graph_relations = relations.get('graph_relations', {})

    output_lines = [
        f"🔗 实体 '{entity_name}' 的关系网络:",
        f"   相关记忆数量: {memory_count}"
    ]

    # 显示图数据库信息
    if graph_relations:
        entity_count = graph_relations.get('entity_count', 0)
        search_count = graph_relations.get('search_count', 0)
        search_results = graph_relations.get('search_results', [])

        output_lines.append(f"   图数据库实体: {entity_count}")
        output_lines.append(f"   图搜索结果: {search_count}")

        # 🔥 显示图关系详情（只显示实体名称，排除自己，最多15条）
        if search_results:
            output_lines.append("   图关系详情:")
            entity_names = []
            for result in search_results:
                entity = result.get('entity', 'Unknown')
                # 排除查询实体本身和无效实体
                if (entity != 'Unknown' and
                    entity not in entity_names and
                    entity.lower() != entity_name.lower()):
                    entity_names.append(entity)

            # 用序号列出相关实体
            for i, entity in enumerate(entity_names[:15], 1):
                output_lines.append(f"     {i}. {entity}")

            if entity_names:
                output_lines.append("   💡 提示: 可搜索上述实体获取具体关系和记忆详情")

    if related_memories:
        output_lines.append("   相关记忆:")
        # 显示相关记忆结果（最多50条）
        memory_limit = min(len(related_memories), 50)
        for i, mem in enumerate(related_memories[:memory_limit], 1):
            memory_text = mem.get('memory', 'N/A')
            output_lines.append(f"     {i}. {memory_text}")

    return "\n".join(output_lines)

def format_extract_entities_result(result):
    """格式化实体提取结果"""
    if not result or result.get('status') != 'success':
        return f"❌ 实体提取失败: {result.get('message', '未知错误')}"

    entities = result.get('entities', [])
    deleted_entities = result.get('deleted_entities', [])
    graph_result = result.get('graph_result', {})

    output_lines = [
        "🔍 实体提取结果:"
    ]

    if entities:
        output_lines.append(f"   新增实体: {len(entities)}")
        for i, entity in enumerate(entities, 1):
            output_lines.append(f"     {i}. {entity}")
    else:
        output_lines.append("   新增实体: 0")

    if deleted_entities:
        output_lines.append(f"   删除实体: {len(deleted_entities)}")
        for i, entity in enumerate(deleted_entities, 1):
            output_lines.append(f"     {i}. {entity}")

    if graph_result:
        output_lines.append(f"   图数据库操作: 成功")

    return "\n".join(output_lines)

def format_process_episode_result(result):
    """格式化情节处理结果"""
    if not result or result.get('status') != 'success':
        return f"❌ 情节处理失败: {result.get('message', '未知错误')}"

    episode = result.get('episode', {})
    if not episode:
        return "❌ 情节数据为空"

    episode_id = episode.get('id', 'N/A')
    content = episode.get('content', 'N/A')
    entities_count = episode.get('entities_count', 0)
    relations_count = episode.get('relations_count', 0)
    entities = episode.get('entities', [])
    relations = episode.get('relations', [])

    output_lines = [
        "📖 情节处理完成:",
        f"   情节ID: {episode_id}",
        f"   内容: {content[:100]}{'...' if len(content) > 100 else ''}",
        f"   提取实体: {entities_count}",
        f"   识别关系: {relations_count}"
    ]

    if entities:
        output_lines.append("   实体列表:")
        for i, entity in enumerate(entities[:5], 1):
            name = entity.get('name', 'N/A')
            entity_type = entity.get('type', 'N/A')
            confidence = entity.get('confidence', 0)
            output_lines.append(f"     {i}. {name} ({entity_type}) - 置信度: {confidence:.2f}")

        if len(entities) > 5:
            output_lines.append(f"     ... 还有 {len(entities) - 5} 个实体")

    if relations:
        output_lines.append("   关系列表:")
        for i, relation in enumerate(relations, 1):  # 显示全部关系
            source = relation.get('source', 'N/A')
            target = relation.get('target', 'N/A')
            rel_type = relation.get('type', 'N/A')
            confidence = relation.get('confidence', 0)
            output_lines.append(f"     {i}. {source} --{rel_type}--> {target} (置信度: {confidence:.2f})")

    return "\n".join(output_lines)

def format_hybrid_search_result(result):
    """格式化混合搜索结果"""
    if not result or result.get('status') != 'success':
        return f"❌ 混合搜索失败: {result.get('message', '未知错误')}"

    results = result.get('results', [])
    vector_count = result.get('vector_count', 0)
    graph_count = result.get('graph_count', 0)
    total_count = result.get('total_count', 0)

    output_lines = [
        "🔍 混合搜索结果:",
        f"   向量搜索: {vector_count} 条",
        f"   图搜索: {graph_count} 条",
        f"   总计: {total_count} 条结果"
    ]

    if results:
        output_lines.append("   搜索结果:")
        # 🔧 修复：分离搜索结果和推荐，避免重复
        actual_results = [item for item in results if item.get('source') in ['vector', 'graph']]
        recommendations = [item for item in results if item.get('source') == 'recommendation']

        # 显示实际搜索结果（最多50条）
        search_limit = min(len(actual_results), 50)
        for i, item in enumerate(actual_results[:search_limit], 1):
            memory_text = item.get('memory', 'N/A')
            output_lines.append(f"     {i}. {memory_text}")

        # 🔧 推荐逻辑：当搜索结果<15条时，推荐数量基于搜索结果；否则最多15条
        if actual_results:
            if len(actual_results) < 15:
                max_recommendations = len(actual_results)  # 基于搜索结果数量
            else:
                max_recommendations = 15  # 最多15条推荐

            # 获取搜索结果的内容，用于去重
            search_contents = {item.get('memory', '') for item in actual_results[:search_limit]}

            # 过滤掉与搜索结果重复的推荐
            unique_recommendations = [
                item for item in recommendations
                if item.get('memory', '') not in search_contents
            ]

            if unique_recommendations and max_recommendations > 0:
                output_lines.append("   相关推荐:")
                for i, item in enumerate(unique_recommendations[:max_recommendations], 1):
                    memory_text = item.get('memory', 'N/A')
                    output_lines.append(f"     {i}. {memory_text}")
    else:
        output_lines.append("   未找到相关结果")

    return "\n".join(output_lines)

def format_intelligent_recommendations_result(result):
    """格式化智能推荐结果"""
    if not result or result.get('status') != 'success':
        return f"❌ 智能推荐失败: {result.get('message', '未知错误')}"

    recommendations = result.get('recommendations', [])
    recommendation_type = result.get('type', 'unknown')
    count = result.get('count', 0)

    if not recommendations:
        return "💡 暂无推荐内容"

    # 类型映射
    type_mapping = {
        'personalized': '个性化推荐',
        'discovery': '探索发现',
        'real_time': '实时推荐'
    }

    type_display = type_mapping.get(recommendation_type, recommendation_type)

    output_lines = [
        f"💡 {type_display} ({count}条):"
    ]

    for i, rec in enumerate(recommendations, 1):
        content = rec.get('content', 'N/A')
        reason = rec.get('reason', '推荐')
        confidence = rec.get('confidence', 0)

        # 简化显示，只显示内容和原因
        output_lines.append(f"   {i}. {content}")
        if reason != '推荐':  # 只在有特殊原因时显示
            output_lines.append(f"      ({reason})")

    return "\n".join(output_lines)

def format_memory_result(result, operation_type):
    """统一格式化记忆操作结果"""
    if operation_type == "add":
        return format_add_memory_result(result)
    elif operation_type == "search":
        return format_search_result(result)
    elif operation_type == "list":
        return format_list_memories_result(result)
    elif operation_type == "delete_all":
        return format_delete_result(result, "all")
    elif operation_type == "delete_single":
        return format_delete_result(result, "single")

    elif operation_type == "update":
        return format_update_result(result)
    elif operation_type == "relations":
        return format_relations_result(result)
    elif operation_type == "entity_relations":
        return format_entity_relations_result(result)
    elif operation_type == "extract_entities":
        return format_extract_entities_result(result)
    elif operation_type == "process_episode":
        return format_process_episode_result(result)
    elif operation_type == "hybrid_search":
        return format_hybrid_search_result(result)
    elif operation_type == "intelligent_recommendations":
        return format_intelligent_recommendations_result(result)
    else:
        return str(result)

# 测试函数
if __name__ == "__main__":
    # 测试添加记忆格式化
    test_add_result = {
        "status": "success",
        "message": "记忆添加成功",
        "result": {
            "results": [
                {
                    "id": "d686bb33-d468-43f5-b901-3dd39c4b9e0d",
                    "memory": "使用Python开发项目",
                    "event": "UPDATE",
                    "previous_memory": "喜欢编程"
                }
            ],
            "relations": {
                "deleted_entities": [],
                "added_entities": []
            }
        }
    }
    
    print("=== 格式化测试 ===")
    print(format_add_memory_result(test_add_result))
